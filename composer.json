{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "version": "2.4.1", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.4", "ext-intl": "*", "ext-pgsql": "*", "ext-sockets": "*", "ext-sodium": "*", "ext-zip": "*", "andreia/filament-nord-theme": "^1.0", "andrewdwallo/filament-selectify": "^2.0", "awcodes/filament-table-repeater": "^3.1", "barryvdh/laravel-dompdf": "^3.1", "barryvdh/laravel-snappy": "^1.0", "bezhansalleh/filament-shield": "^3.3", "bytexr/filament-queueable-bulk-actions": "^0.3.4", "charrafimed/global-search-modal": "^3.7", "chrome-php/chrome": "^1.13", "cmsmaxinc/filament-error-pages": "^1.0", "cweagans/composer-patches": "*", "dedoc/scramble": "^0.12.19", "devonab/filament-easy-footer": "^1.0", "discoverydesign/filament-gaze": "^1.1", "endroid/qr-code": "^6.0", "filafly/phosphor-icon-replacement": "^1.0", "filament/filament": "^3.2", "guava/filament-icon-select-column": "^1.1", "guava/filament-knowledge-base": "^1.14", "icetalker/filament-table-repeatable-entry": "^1.0", "icetalker/filament-table-repeater": "^1.3", "joaopaulolndev/filament-general-settings": "^1.0", "laravel/framework": "^11.31", "laravel/horizon": "^5.33", "laravel/nightwatch": "^1.7", "laravel/octane": "^2.9", "laravel/reverb": "^1.4", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "mansoor/filament-versionable": "^0.0.13", "monzer/filament-chatify-integration": "^1.0", "munafio/chatify": "^1.6", "njxqlus/filament-progressbar": "^1.1", "nuxtifyts/dash-stack-theme": "^1.0", "owenvoke/blade-fontawesome": "^2.9", "predis/predis": "^2.3", "quix-labs/laravel-supabase-flysystem": "^1.0", "rawilk/profile-filament-plugin": "^0.5.4", "rupadana/filament-api-service": "^3.4", "saade/filament-autograph": "^3.1", "shuvroroy/filament-spatie-laravel-backup": "^2.2", "spatie/browsershot": "^5.0", "spatie/laravel-passkeys": "^1.0", "spatie/shiki-php": "^2.0", "vormkracht10/filament-mails": "^2.2", "ysfkaya/filament-phone-input": "^3.1"}, "require-dev": {"driftingly/rector-laravel": "^2.0", "fakerphp/faker": "^1.23", "kitloong/laravel-migrations-generator": "^7.0", "larastan/larastan": "^3.5", "laravel/pail": "^1.1", "laravel/pint": "^1.22", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "pestphp/pest": "^3.8", "pestphp/pest-plugin-laravel": "^3.1", "pestphp/pest-plugin-type-coverage": "^3.5", "rector/rector": "^2.0", "reliese/laravel": "^1.3", "wire-elements/wire-spy": "^0.0.12"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"], "lint": ["./vendor/bin/pint --ansi", "./vendor/bin/rector --ansi", "./vendor/bin/pest --parallel", "./vendor/bin/pest --type-coverage --ansi --memory-limit=-1 --min=100", "./vendor/bin/phpstan analyse -c phpstan.neon --ansi --memory-limit=-1"]}, "extra": {"laravel": {"dont-discover": ["kitloong/laravel-migrations-generator"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"cweagans/composer-patches": true, "pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}