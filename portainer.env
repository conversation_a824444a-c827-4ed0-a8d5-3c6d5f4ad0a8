# Portainer Environment Variables Template
# Copy this file and configure your values before deploying the stack

# Application Configuration
APP_NAME=DCCP Admin V2
APP_KEY=base64:your_generated_key_here
APP_URL=https://your-domain.com
APP_PORT=8000
DOMAIN=your-domain.com
APP_IMAGE=ghcr.io/YOUR_GITHUB_USERNAME/dccp-admin:latest # IMPORTANT: Replace YOUR_GITHUB_USERNAME

# Database Configuration (External PostgreSQL)
DB_HOST=your-postgres-host.com
DB_PORT=5432
DB_DATABASE=dccpadminv2
DB_USERNAME=dccp_user
DB_PASSWORD=your_secure_database_password

# Redis Configuration (External Redis)
REDIS_HOST=your-redis-host.com
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password

# Optional: Include services (set to true to include Redis/PostgreSQL in stack)
INCLUDE_REDIS=false
INCLUDE_POSTGRES=false
REDIS_EXTERNAL_PORT=6379
POSTGRES_EXTERNAL_PORT=5432

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailgun.org
MAIL_PORT=587
MAIL_USERNAME=your_mail_username
MAIL_PASSWORD=your_mail_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=DCCP Admin

# Logging Level
LOG_LEVEL=warning

# Storage Path (for bind mounts)
STORAGE_PATH=/opt/dccp-admin/storage

# Optional: Auto-run migrations on startup
RUN_MIGRATIONS=false

# Resource Limits
APP_MEMORY_LIMIT=1G
APP_CPU_LIMIT=2.0
WORKER_CPU_LIMIT=1.0
WORKER_MEMORY_LIMIT=512M
REDIS_MEMORY_LIMIT=512M
POSTGRES_MEMORY_LIMIT=1G